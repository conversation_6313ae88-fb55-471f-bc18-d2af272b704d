#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Pydantic数据模型
"""

from typing import Optional, List, Dict, Any
from pydantic import BaseModel, Field

class GenerateImageRequest(BaseModel):
    """AI绘画请求模型"""
    prompt: str = Field(..., description="绘画提示词", min_length=1, max_length=500)
    style: Optional[str] = Field(None, description="绘画风格")
    ratio: Optional[str] = Field(None, description="图片比例")

class UpscaleImageRequest(BaseModel):
    """图片放大请求模型"""
    image_id: str = Field(..., description="图片ID")
    index: int = Field(..., description="图片序号(1-4)", ge=1, le=4)

class EditWithReferenceRequest(BaseModel):
    """参考图编辑请求模型"""
    prompt: str = Field(..., description="编辑提示词", min_length=1, max_length=500)
    style: Optional[str] = Field(None, description="绘画风格")
    ratio: Optional[str] = Field(None, description="图片比例")

class InpaintRequest(BaseModel):
    """区域重绘请求模型"""
    prompt: str = Field(..., description="重绘提示词", min_length=1, max_length=500)
    mode: str = Field("circle", description="模式", pattern="^(circle|brush)$")
    invert: bool = Field(False, description="是否反选")

class ImageResponse(BaseModel):
    """图片响应模型"""
    success: bool = Field(..., description="是否成功")
    error: Optional[str] = Field(None, description="错误信息")
    image_id: Optional[str] = Field(None, description="图片ID")
    urls: Optional[List[str]] = Field(None, description="图片URL列表")
    count: Optional[int] = Field(None, description="图片数量")

class UpscaleResponse(BaseModel):
    """图片放大响应模型"""
    success: bool = Field(..., description="是否成功")
    error: Optional[str] = Field(None, description="错误信息")
    image_url: Optional[str] = Field(None, description="图片URL")
    original_image_id: Optional[str] = Field(None, description="原始图片ID")
    index: Optional[int] = Field(None, description="图片序号")

class RemoveBackgroundResponse(BaseModel):
    """抠图响应模型"""
    success: bool = Field(..., description="是否成功")
    error: Optional[str] = Field(None, description="错误信息")
    image_id: Optional[str] = Field(None, description="图片ID")
    image_url: Optional[str] = Field(None, description="图片URL")
    mask: Optional[str] = Field(None, description="蒙版数据")
    without_background: Optional[bool] = Field(None, description="是否去除背景")

class ImageInfo(BaseModel):
    """图片信息模型"""
    id: str = Field(..., description="图片ID")
    urls: List[str] = Field(..., description="图片URL列表")
    type: str = Field(..., description="操作类型")
    operation_params: Dict[str, Any] = Field(..., description="操作参数")
    parent_id: Optional[str] = Field(None, description="父图片ID")
    create_time: int = Field(..., description="创建时间")

class ImageInfoResponse(BaseModel):
    """图片信息响应模型"""
    success: bool = Field(..., description="是否成功")
    error: Optional[str] = Field(None, description="错误信息")
    image_data: Optional[ImageInfo] = Field(None, description="图片信息")

class ImageListResponse(BaseModel):
    """图片列表响应模型"""
    success: bool = Field(..., description="是否成功")
    error: Optional[str] = Field(None, description="错误信息")
    images: Optional[List[ImageInfo]] = Field(None, description="图片列表")
    count: Optional[int] = Field(None, description="图片数量")

class StylesResponse(BaseModel):
    """风格列表响应模型"""
    styles: List[str] = Field(..., description="支持的风格列表")

class RatiosResponse(BaseModel):
    """比例列表响应模型"""
    ratios: List[str] = Field(..., description="支持的比例列表")

class NewSessionResponse(BaseModel):
    """新建会话响应模型"""
    success: bool = Field(..., description="是否成功")
    error: Optional[str] = Field(None, description="错误信息")
    conversation_id: Optional[str] = Field(None, description="会话ID")
    section_id: Optional[str] = Field(None, description="节ID")

class BaseResponse(BaseModel):
    """基础响应模型"""
    success: bool = Field(..., description="是否成功")
    error: Optional[str] = Field(None, description="错误信息")
    message: Optional[str] = Field(None, description="响应消息")
