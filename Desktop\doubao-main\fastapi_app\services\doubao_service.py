#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
豆包核心业务逻辑服务
"""

import json
import os
import time
import uuid
import base64
from typing import Optional, List, Dict, Any
from token_manager import TokenManager
from api_client import ApiClient
from image_storage import ImageStorage
from image_processor import ImageProcessor
from image_uploader import ImageUploader
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(__file__)))
from utils.logger import logger
from utils.file_utils import FileUtils

class DoubaoService:
    """豆包AI绘画服务"""
    
    def __init__(self, config_path: str, storage_dir: str, temp_dir: str):
        """
        初始化豆包服务
        
        Args:
            config_path: 配置文件路径
            storage_dir: 存储目录
            temp_dir: 临时文件目录
        """
        self.config = self._load_config(config_path)
        self.storage_dir = storage_dir
        self.temp_dir = temp_dir
        
        # 确保目录存在
        FileUtils.ensure_dir(storage_dir)
        FileUtils.ensure_dir(temp_dir)
        
        # 初始化各个模块
        self.image_storage = ImageStorage(
            os.path.join(storage_dir, "images.db"),
            retention_days=self.config.get("storage", {}).get("retention_days", 7)
        )
        
        self.token_manager = TokenManager(self.config)
        self.api_client = ApiClient(self.token_manager)
        self.image_uploader = ImageUploader(self.config)
        self.image_processor = ImageProcessor(temp_dir, self.image_uploader)
        
        # 从配置文件加载支持的风格列表
        self.styles = self.config.get("styles", [])
        
        # 初始化会话信息
        self.conversation_id = None
        self.section_id = None
        self.reply_id = None
        
        # 从数据库恢复上次会话信息
        self._init_conversation_from_storage()
        
        logger.info("[DoubaoService] 服务初始化完成")
    
    def _load_config(self, config_path: str) -> dict:
        """加载配置文件"""
        try:
            with open(config_path, "r", encoding='utf-8') as f:
                config = json.load(f)
                logger.info(f"[DoubaoService] 配置文件加载成功: {config_path}")
                return config
        except Exception as e:
            logger.error(f"[DoubaoService] 配置文件加载失败: {e}")
            return {}
    
    def _init_conversation_from_storage(self):
        """从存储中恢复会话信息"""
        try:
            # 从配置文件中获取会话信息
            session_info = self.config.get("session", {})
            if session_info:
                self.conversation_id = session_info.get("conversation_id")
                self.section_id = session_info.get("section_id")
                logger.info(f"[DoubaoService] 恢复会话信息: {self.conversation_id}")
        except Exception as e:
            logger.error(f"[DoubaoService] 恢复会话信息失败: {e}")
    
    def _parse_style_and_ratio(self, content: str) -> tuple:
        """解析绘画指令中的风格和比例参数"""
        prompt = content.strip()
        style = None
        ratio = self.config.get("params", {}).get("default_ratio", "4:3")
        supported_ratios = self.config.get("params", {}).get("ratios", ["1:1", "2:3", "4:3", "9:16", "16:9"])
        
        # 处理自然语言描述格式
        if "图风格为" in prompt:
            parts = prompt.split("图风格为")
            if len(parts) == 2:
                prompt = parts[0].strip()
                style_part = parts[1].strip()
                # 提取风格
                if "「" in style_part and "」" in style_part:
                    style = style_part[style_part.find("「")+1:style_part.find("」")]
                else:
                    for s in self.styles:
                        if style_part.startswith(s):
                            style = s
                            break
        
        # 处理比例描述
        if "比例" in prompt:
            parts = prompt.split("比例")
            if len(parts) == 2:
                prompt = parts[0].strip()
                ratio_part = parts[1].strip()
                # 提取比例
                if "「" in ratio_part and "」" in ratio_part:
                    ratio = ratio_part[ratio_part.find("「")+1:ratio_part.find("」")]
                else:
                    for r in supported_ratios:
                        if ratio_part.startswith(r):
                            ratio = r
                            break
        
        # 处理分隔符格式 (-或空格或逗号)
        if not style or not ratio:
            # 将中文冒号替换为英文冒号
            prompt = prompt.replace("：", ":")
            
            # 分割所有可能的分隔符
            parts = []
            for sep in ["-", " ", ","]:
                if sep in prompt:
                    parts.extend([p.strip() for p in prompt.split(sep) if p.strip()])
            
            if parts:
                # 最后两个部分可能是风格和比例
                last_parts = parts[-2:]
                prompt = " ".join(parts[:-2]) if len(parts) > 2 else parts[0]
                
                for part in last_parts:
                    # 检查是否是比例格式
                    if ":" in part and part in supported_ratios:
                        ratio = part
                    # 检查是否是支持的风格
                    elif part in self.styles:
                        style = part
        
        return prompt.strip(), style, ratio.replace("：", ":")
    
    def create_new_conversation(self) -> bool:
        """创建新的图像生成会话"""
        try:
            # 构建图像生成会话请求数据
            data = {
                "skill_type": 3,
                "condition": {
                    "image_condition": {
                        "category_id": 0
                    }
                }
            }
            
            result = self.api_client.send_request(data, "/samantha/skill/pack")
            if result and "data" in result and "image" in result["data"]:
                # 获取图像生成相关的会话信息
                self.styles = []
                if "meta" in result["data"]["image"]:
                    category_list = result["data"]["image"]["meta"].get("category_list", [])
                    self.styles = [category["category_name"] for category in category_list]
                    logger.info(f"[DoubaoService] 从API加载了 {len(self.styles)} 种风格")
                
                # 获取历史会话列表
                history_data = {
                    "request_list": [{
                        "conversation_id": "0"
                    }]
                }
                history_result = self.api_client.send_request(history_data, "/alice/conversation/latest_messagelist")
                if history_result and "data" in history_result and "message_map" in history_result["data"]:
                    # 找到最新的图像生成会话
                    for conv_id, messages in history_result["data"]["message_map"].items():
                        if messages and len(messages) > 0:
                            self.conversation_id = conv_id
                            self.section_id = messages[0].get("section_id")
                            break
                
                logger.info(f"[DoubaoService] 新会话创建成功: {self.conversation_id}")
                return True
            return False
        except Exception as e:
            logger.error(f"[DoubaoService] 创建新会话失败: {e}")
            return False
    
    def generate_image(self, prompt: str, style: Optional[str] = None, ratio: Optional[str] = None) -> Dict[str, Any]:
        """
        生成AI绘画
        
        Args:
            prompt: 绘画提示词
            style: 绘画风格
            ratio: 图片比例
            
        Returns:
            生成结果字典
        """
        try:
            logger.info(f"[DoubaoService] 开始生成图片: {prompt}")
            
            # 解析风格和比例
            if not style or not ratio:
                parsed_prompt, parsed_style, parsed_ratio = self._parse_style_and_ratio(prompt)
                prompt = parsed_prompt
                style = style or parsed_style
                ratio = ratio or parsed_ratio
            
            # 确保有会话
            if not self.conversation_id:
                if not self.create_new_conversation():
                    return {"success": False, "error": "无法创建会话"}
            
            # 构建绘画请求
            content_data = {"text": prompt}
            
            # 添加风格
            if style and style in self.styles:
                content_data["style"] = style
                logger.info(f"[DoubaoService] 使用风格: {style}")
            
            # 添加比例
            if ratio:
                content_data["ratio"] = ratio
                logger.info(f"[DoubaoService] 使用比例: {ratio}")
            
            data = {
                "messages": [{
                    "content": json.dumps(content_data),
                    "content_type": 2008,
                    "attachments": []
                }],
                "completion_option": {
                    "is_regen": False,
                    "with_suggest": False,
                    "need_create_conversation": False,
                    "launch_stage": 1,
                    "is_replace": False,
                    "is_delete": False,
                    "message_from": 0,
                    "event_id": "0"
                },
                "section_id": self.section_id,
                "conversation_id": self.conversation_id,
                "local_message_id": str(uuid.uuid1())
            }
            
            # 发送请求
            result = self.api_client.send_request(data)
            if result and "urls" in result and result["urls"]:
                # 更新会话信息
                if result.get("conversation_id"):
                    self.conversation_id = result["conversation_id"]
                if result.get("section_id"):
                    self.section_id = result["section_id"]
                if result.get("reply_id"):
                    self.reply_id = result["reply_id"]
                
                # 存储图片信息
                img_id = str(int(time.time()))
                operation_params = {
                    "prompt": prompt,
                    "style": style,
                    "ratio": ratio,
                    "conversation_id": self.conversation_id,
                    "section_id": self.section_id,
                    "reply_id": self.reply_id
                }
                
                image_info = {
                    "urls": result["urls"],
                    "type": "generate",
                    "operation_params": operation_params,
                    "parent_id": None,
                    "create_time": int(time.time())
                }
                
                self.image_storage.store_image(img_id, image_info)
                
                logger.info(f"[DoubaoService] 图片生成成功，ID: {img_id}")
                return {
                    "success": True,
                    "image_id": img_id,
                    "urls": result["urls"],
                    "count": len(result["urls"])
                }
            else:
                logger.error(f"[DoubaoService] 图片生成失败: {result}")
                return {"success": False, "error": "生成失败，未返回图片URL"}
                
        except Exception as e:
            logger.error(f"[DoubaoService] 图片生成异常: {e}")
            return {"success": False, "error": str(e)}

    def upscale_image(self, image_id: str, index: int) -> Dict[str, Any]:
        """
        放大图片

        Args:
            image_id: 图片ID
            index: 图片序号(1-4)

        Returns:
            放大结果字典
        """
        try:
            logger.info(f"[DoubaoService] 开始放大图片: {image_id}, 序号: {index}")

            # 验证图片和序号
            is_valid, error_msg = self.image_storage.validate_image_index(image_id, index)
            if not is_valid:
                return {"success": False, "error": error_msg}

            # 获取图片信息
            image_data = self.image_storage.get_image(image_id)
            if not image_data:
                return {"success": False, "error": "找不到对应的图片ID"}

            # 获取指定序号的图片URL
            image_url = image_data["urls"][index - 1]

            logger.info(f"[DoubaoService] 图片放大成功: {image_url}")
            return {
                "success": True,
                "image_url": image_url,
                "original_image_id": image_id,
                "index": index
            }

        except Exception as e:
            logger.error(f"[DoubaoService] 图片放大异常: {e}")
            return {"success": False, "error": str(e)}

    def edit_with_reference(self, image_bytes: bytes, prompt: str, style: Optional[str] = None, ratio: Optional[str] = None) -> Dict[str, Any]:
        """
        基于参考图编辑

        Args:
            image_bytes: 参考图片字节数据
            prompt: 编辑提示词
            style: 绘画风格
            ratio: 图片比例

        Returns:
            编辑结果字典
        """
        try:
            logger.info(f"[DoubaoService] 开始参考图编辑: {prompt}")

            # 解析风格和比例
            if not style or not ratio:
                parsed_prompt, parsed_style, parsed_ratio = self._parse_style_and_ratio(prompt)
                prompt = parsed_prompt
                style = style or parsed_style
                ratio = ratio or parsed_ratio

            # 上传参考图片
            result = self.image_uploader.upload_and_process_image(image_bytes)
            if not result or not result.get('success'):
                error_msg = result.get('error') if result else "未知错误"
                logger.error(f"[DoubaoService] 参考图上传失败: {error_msg}")
                return {"success": False, "error": "参考图上传失败"}

            image_key = result.get('image_key')
            main_url = result.get('file_info', {}).get('main_url', '')

            if not image_key or not main_url:
                return {"success": False, "error": "上传结果异常"}

            # 确保有会话
            if not self.conversation_id:
                if not self.create_new_conversation():
                    return {"success": False, "error": "无法创建会话"}

            # 构建参考图编辑请求
            content_data = {
                "text": prompt,
                "reference_image": {
                    "reference_image_url": main_url,
                    "reference_image_token": image_key.split("/")[-1].split(".")[0],
                    "description": ""
                }
            }

            # 添加风格
            if style and style in self.styles:
                content_data["style"] = style

            # 添加比例
            if ratio:
                content_data["ratio"] = ratio

            data = {
                "messages": [{
                    "content": json.dumps(content_data),
                    "content_type": 2008,
                    "attachments": []
                }],
                "completion_option": {
                    "is_regen": False,
                    "with_suggest": False,
                    "need_create_conversation": False,
                    "launch_stage": 1,
                    "is_replace": False,
                    "is_delete": False,
                    "message_from": 0,
                    "event_id": "0"
                },
                "section_id": self.section_id,
                "conversation_id": self.conversation_id,
                "local_message_id": str(uuid.uuid1())
            }

            # 发送请求
            api_result = self.api_client.send_request(data)
            if api_result and "urls" in api_result and api_result["urls"]:
                # 更新会话信息
                if api_result.get("conversation_id"):
                    self.conversation_id = api_result["conversation_id"]
                if api_result.get("section_id"):
                    self.section_id = api_result["section_id"]
                if api_result.get("reply_id"):
                    self.reply_id = api_result["reply_id"]

                # 存储图片信息
                img_id = str(int(time.time()))
                operation_params = {
                    "prompt": prompt,
                    "style": style,
                    "ratio": ratio,
                    "reference_image_key": image_key,
                    "reference_image_url": main_url,
                    "conversation_id": self.conversation_id,
                    "section_id": self.section_id,
                    "reply_id": self.reply_id
                }

                image_info = {
                    "urls": api_result["urls"],
                    "type": "reference_edit",
                    "operation_params": operation_params,
                    "parent_id": None,
                    "create_time": int(time.time())
                }

                self.image_storage.store_image(img_id, image_info)

                logger.info(f"[DoubaoService] 参考图编辑成功，ID: {img_id}")
                return {
                    "success": True,
                    "image_id": img_id,
                    "urls": api_result["urls"],
                    "count": len(api_result["urls"])
                }
            else:
                logger.error(f"[DoubaoService] 参考图编辑失败: {api_result}")
                return {"success": False, "error": "编辑失败，未返回图片URL"}

        except Exception as e:
            logger.error(f"[DoubaoService] 参考图编辑异常: {e}")
            return {"success": False, "error": str(e)}

    def remove_background(self, image_bytes: bytes) -> Dict[str, Any]:
        """
        抠图功能

        Args:
            image_bytes: 图片字节数据

        Returns:
            抠图结果字典
        """
        try:
            logger.info("[DoubaoService] 开始抠图处理")

            # 上传图片到豆包服务器
            result = self.image_uploader.upload_and_process_image(image_bytes)

            if not result or not result.get('success'):
                error_msg = result.get('error') if result else "未知错误"
                logger.error(f"[DoubaoService] 图片上传失败: {error_msg}")
                return {"success": False, "error": "图片上传失败"}

            image_key = result.get('image_key')
            main_url = result.get('file_info', {}).get('main_url', '')

            if not image_key or not main_url:
                return {"success": False, "error": "上传结果异常"}

            # 存储抠图结果
            img_id = str(int(time.time()))
            operation_params = {
                "image_key": image_key,
                "conversation_id": self.conversation_id,
                "section_id": self.section_id,
                "image_token": image_key.split("/")[-1].split(".")[0],
                "image_url": main_url,
                "original_url": main_url,
                "mask": result.get('mask', ''),
                "without_background": result.get('without_background', False)
            }

            # 存储图片信息
            image_info = {
                "urls": [main_url],
                "type": "remove_background",
                "operation_params": operation_params,
                "parent_id": None,
                "create_time": int(time.time())
            }

            self.image_storage.store_image(img_id, image_info)

            logger.info(f"[DoubaoService] 抠图处理成功，ID: {img_id}")
            return {
                "success": True,
                "image_id": img_id,
                "image_url": main_url,
                "mask": result.get('mask', ''),
                "without_background": result.get('without_background', False)
            }

        except Exception as e:
            logger.error(f"[DoubaoService] 抠图处理异常: {e}")
            return {"success": False, "error": str(e)}

    def inpaint_image(self, original_image_bytes: bytes, marked_image_bytes: bytes,
                     prompt: str, mode: str = "circle", invert: bool = False) -> Dict[str, Any]:
        """
        区域重绘

        Args:
            original_image_bytes: 原始图片字节数据
            marked_image_bytes: 标记图片字节数据
            prompt: 重绘提示词
            mode: 模式 ("circle" 或 "brush")
            invert: 是否反选

        Returns:
            重绘结果字典
        """
        try:
            logger.info(f"[DoubaoService] 开始区域重绘: {prompt}, 模式: {mode}")

            # 上传原始图片
            upload_result = self.image_uploader.upload_and_process_image(original_image_bytes)
            if not upload_result or not upload_result.get('success'):
                error_msg = upload_result.get('error') if upload_result else "未知错误"
                logger.error(f"[DoubaoService] 原始图片上传失败: {error_msg}")
                return {"success": False, "error": "原始图片上传失败"}

            image_key = upload_result.get('image_key')
            image_url = upload_result.get('file_info', {}).get('main_url', '')

            if not image_key or not image_url:
                return {"success": False, "error": "原始图片上传结果异常"}

            # 生成蒙版
            if mode == "circle":
                mask_base64 = self.image_processor.create_mask_from_circle_selection(
                    original_image_bytes, marked_image_bytes, invert
                )
            else:  # brush mode
                mask_base64 = self.image_processor.create_mask_from_marked_image(
                    original_image_bytes, marked_image_bytes
                )

            if not mask_base64:
                return {"success": False, "error": "蒙版生成失败"}

            # 确保有会话
            if not self.conversation_id:
                if not self.create_new_conversation():
                    return {"success": False, "error": "无法创建会话"}

            # 构建重绘请求
            content_data = {
                "text": prompt,
                "edit_image": {
                    "edit_image_url": image_url,
                    "edit_image_token": image_key.split("/")[-1].split(".")[0],
                    "description": "",
                    "ability": "inpainting",
                    "mask": mask_base64,
                    "is_edit_local_image": True,
                    "is_edit_local_image_v2": "true"
                }
            }

            data = {
                "messages": [{
                    "content": json.dumps(content_data),
                    "content_type": 2009,
                    "attachments": []
                }],
                "completion_option": {
                    "is_regen": False,
                    "with_suggest": False,
                    "need_create_conversation": False,
                    "launch_stage": 1,
                    "is_replace": False,
                    "is_delete": False,
                    "message_from": 0,
                    "event_id": "0"
                },
                "section_id": self.section_id,
                "conversation_id": self.conversation_id,
                "local_message_id": str(uuid.uuid1())
            }

            # 发送重绘请求
            result = self.api_client.send_request(data, "/samantha/chat/completion")
            if result and "urls" in result:
                # 更新会话信息
                if result.get("conversation_id"):
                    self.conversation_id = result["conversation_id"]
                if result.get("section_id"):
                    self.section_id = result["section_id"]

                # 存储重绘后的图片
                img_id = str(int(time.time()))
                operation_params = {
                    "prompt": prompt,
                    "conversation_id": self.conversation_id,
                    "section_id": self.section_id,
                    "reply_id": result.get("reply_id"),
                    "image_token": result["urls"][0].split("/")[-1].split("~")[0],
                    "image_url": result["urls"][0],
                    "original_key": image_key,
                    "original_url": image_url,
                    "mask": mask_base64,
                    "mode": mode,
                    "is_invert": invert
                }

                image_info = {
                    "urls": result["urls"],
                    "type": "inpaint",
                    "operation_params": operation_params,
                    "parent_id": None,
                    "create_time": int(time.time())
                }

                self.image_storage.store_image(img_id, image_info)

                logger.info(f"[DoubaoService] 区域重绘成功，ID: {img_id}")
                return {
                    "success": True,
                    "image_id": img_id,
                    "urls": result["urls"],
                    "count": len(result["urls"])
                }
            else:
                logger.error(f"[DoubaoService] 区域重绘失败: {result}")
                return {"success": False, "error": "重绘失败，未返回图片URL"}

        except Exception as e:
            logger.error(f"[DoubaoService] 区域重绘异常: {e}")
            return {"success": False, "error": str(e)}

    def get_image_info(self, image_id: str) -> Dict[str, Any]:
        """
        获取图片信息

        Args:
            image_id: 图片ID

        Returns:
            图片信息字典
        """
        try:
            image_data = self.image_storage.get_image(image_id)
            if not image_data:
                return {"success": False, "error": "找不到对应的图片ID"}

            return {
                "success": True,
                "image_data": image_data
            }

        except Exception as e:
            logger.error(f"[DoubaoService] 获取图片信息异常: {e}")
            return {"success": False, "error": str(e)}

    def list_images(self, limit: int = 50) -> Dict[str, Any]:
        """
        获取图片列表

        Args:
            limit: 限制数量

        Returns:
            图片列表字典
        """
        try:
            images = self.image_storage.get_all_images(limit)
            return {
                "success": True,
                "images": images,
                "count": len(images)
            }

        except Exception as e:
            logger.error(f"[DoubaoService] 获取图片列表异常: {e}")
            return {"success": False, "error": str(e)}

    def get_supported_styles(self) -> List[str]:
        """获取支持的风格列表"""
        return self.styles

    def get_supported_ratios(self) -> List[str]:
        """获取支持的比例列表"""
        return self.config.get("params", {}).get("ratios", ["1:1", "2:3", "4:3", "9:16", "16:9"])
