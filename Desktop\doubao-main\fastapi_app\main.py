#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
豆包AI绘画FastAPI应用
"""

import os
import asyncio
from typing import List
from fastapi import FastAPI, File, UploadFile, HTTPException, Depends
from fastapi.responses import FileResponse, JSONResponse
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
import uvicorn

from models import *
from services.doubao_service import DoubaoService
from utils.logger import logger
from utils.file_utils import FileUtils

# 创建FastAPI应用
app = FastAPI(
    title="豆包AI绘画API",
    description="豆包AI绘画服务API接口",
    version="1.0.0"
)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 全局变量
doubao_service: DoubaoService = None

def get_doubao_service() -> DoubaoService:
    """获取豆包服务实例"""
    global doubao_service
    if doubao_service is None:
        raise HTTPException(status_code=500, detail="服务未初始化")
    return doubao_service

@app.on_event("startup")
async def startup_event():
    """应用启动事件"""
    global doubao_service
    try:
        # 获取配置路径
        config_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), "config.json")
        storage_dir = os.path.join(os.path.dirname(__file__), "storage")
        temp_dir = os.path.join(os.path.dirname(__file__), "temp")
        
        # 初始化豆包服务
        doubao_service = DoubaoService(config_path, storage_dir, temp_dir)
        logger.info("[FastAPI] 豆包服务初始化完成")
        
    except Exception as e:
        logger.error(f"[FastAPI] 服务初始化失败: {e}")
        raise

@app.on_event("shutdown")
async def shutdown_event():
    """应用关闭事件"""
    logger.info("[FastAPI] 应用正在关闭")

# 挂载静态文件
app.mount("/static", StaticFiles(directory=os.path.join(os.path.dirname(__file__), "static")), name="static")

@app.get("/", response_model=BaseResponse)
async def root():
    """根路径"""
    return BaseResponse(success=True, message="豆包AI绘画API服务正在运行")

@app.get("/api/health", response_model=BaseResponse)
async def health_check():
    """健康检查"""
    return BaseResponse(success=True, message="服务正常")

@app.post("/api/generate", response_model=ImageResponse)
async def generate_image(
    request: GenerateImageRequest,
    service: DoubaoService = Depends(get_doubao_service)
):
    """AI绘画生成"""
    try:
        logger.info(f"[API] 收到绘画请求: {request.prompt}")
        result = service.generate_image(request.prompt, request.style, request.ratio)
        return ImageResponse(**result)
    except Exception as e:
        logger.error(f"[API] 绘画生成异常: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/upscale", response_model=UpscaleResponse)
async def upscale_image(
    request: UpscaleImageRequest,
    service: DoubaoService = Depends(get_doubao_service)
):
    """图片放大"""
    try:
        logger.info(f"[API] 收到放大请求: {request.image_id}, 序号: {request.index}")
        result = service.upscale_image(request.image_id, request.index)
        return UpscaleResponse(**result)
    except Exception as e:
        logger.error(f"[API] 图片放大异常: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/edit_with_reference", response_model=ImageResponse)
async def edit_with_reference(
    image_file: UploadFile = File(..., description="参考图片文件"),
    prompt: str = "编辑图片",
    style: str = None,
    ratio: str = None,
    service: DoubaoService = Depends(get_doubao_service)
):
    """参考图编辑"""
    try:
        logger.info(f"[API] 收到参考图编辑请求: {prompt}")
        
        # 读取上传的图片
        image_bytes = await image_file.read()
        
        result = service.edit_with_reference(image_bytes, prompt, style, ratio)
        return ImageResponse(**result)
    except Exception as e:
        logger.error(f"[API] 参考图编辑异常: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/remove_background", response_model=RemoveBackgroundResponse)
async def remove_background(
    image_file: UploadFile = File(..., description="需要抠图的图片文件"),
    service: DoubaoService = Depends(get_doubao_service)
):
    """抠图"""
    try:
        logger.info("[API] 收到抠图请求")
        
        # 读取上传的图片
        image_bytes = await image_file.read()
        
        result = service.remove_background(image_bytes)
        return RemoveBackgroundResponse(**result)
    except Exception as e:
        logger.error(f"[API] 抠图异常: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/inpaint", response_model=ImageResponse)
async def inpaint_image(
    original_image: UploadFile = File(..., description="原始图片文件"),
    marked_image: UploadFile = File(..., description="标记图片文件"),
    prompt: str = "重绘区域",
    mode: str = "circle",
    invert: bool = False,
    service: DoubaoService = Depends(get_doubao_service)
):
    """区域重绘"""
    try:
        logger.info(f"[API] 收到区域重绘请求: {prompt}, 模式: {mode}")
        
        # 读取上传的图片
        original_bytes = await original_image.read()
        marked_bytes = await marked_image.read()
        
        result = service.inpaint_image(original_bytes, marked_bytes, prompt, mode, invert)
        return ImageResponse(**result)
    except Exception as e:
        logger.error(f"[API] 区域重绘异常: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/new_session", response_model=NewSessionResponse)
async def new_session(
    service: DoubaoService = Depends(get_doubao_service)
):
    """强制新建会话"""
    try:
        logger.info("[API] 收到新建会话请求")
        success = service.create_new_conversation()
        if success:
            return NewSessionResponse(
                success=True,
                conversation_id=service.conversation_id,
                section_id=service.section_id
            )
        else:
            return NewSessionResponse(success=False, error="创建会话失败")
    except Exception as e:
        logger.error(f"[API] 新建会话异常: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/image/{image_id}", response_model=ImageInfoResponse)
async def get_image_info(
    image_id: str,
    service: DoubaoService = Depends(get_doubao_service)
):
    """获取图片信息"""
    try:
        result = service.get_image_info(image_id)
        return ImageInfoResponse(**result)
    except Exception as e:
        logger.error(f"[API] 获取图片信息异常: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/images", response_model=ImageListResponse)
async def list_images(
    limit: int = 50,
    service: DoubaoService = Depends(get_doubao_service)
):
    """获取图片列表"""
    try:
        result = service.list_images(limit)
        return ImageListResponse(**result)
    except Exception as e:
        logger.error(f"[API] 获取图片列表异常: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/styles", response_model=StylesResponse)
async def get_styles(
    service: DoubaoService = Depends(get_doubao_service)
):
    """获取支持的风格列表"""
    try:
        styles = service.get_supported_styles()
        return StylesResponse(styles=styles)
    except Exception as e:
        logger.error(f"[API] 获取风格列表异常: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/ratios", response_model=RatiosResponse)
async def get_ratios(
    service: DoubaoService = Depends(get_doubao_service)
):
    """获取支持的比例列表"""
    try:
        ratios = service.get_supported_ratios()
        return RatiosResponse(ratios=ratios)
    except Exception as e:
        logger.error(f"[API] 获取比例列表异常: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/download/{image_id}")
async def download_image(
    image_id: str,
    index: int = 1,
    service: DoubaoService = Depends(get_doubao_service)
):
    """下载图片"""
    try:
        # 获取图片信息
        image_data = service.image_storage.get_image(image_id)
        if not image_data:
            raise HTTPException(status_code=404, detail="图片不存在")
        
        # 验证序号
        if index < 1 or index > len(image_data["urls"]):
            raise HTTPException(status_code=400, detail="图片序号无效")
        
        # 获取图片URL
        image_url = image_data["urls"][index - 1]
        
        # 这里应该下载图片并返回文件，暂时返回URL
        return JSONResponse({"image_url": image_url})
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"[API] 下载图片异常: {e}")
        raise HTTPException(status_code=500, detail=str(e))

if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host="127.0.0.1",
        port=8000,
        reload=True,
        log_level="info"
    )
