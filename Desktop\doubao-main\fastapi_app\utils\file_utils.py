#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文件工具模块
"""

import os
import base64
import hashlib
import mimetypes
from pathlib import Path
from typing import Optional, Tuple
from fastapi import UploadFile
import aiofiles

from .logger import logger

class FileUtils:
    """文件工具类"""
    
    @staticmethod
    async def save_upload_file(upload_file: UploadFile, save_dir: str) -> Tuple[bool, str, str]:
        """
        保存上传的文件
        
        Args:
            upload_file: FastAPI上传文件对象
            save_dir: 保存目录
            
        Returns:
            tuple: (是否成功, 文件路径, 错误信息)
        """
        try:
            # 创建保存目录
            os.makedirs(save_dir, exist_ok=True)
            
            # 生成唯一文件名
            file_ext = Path(upload_file.filename).suffix if upload_file.filename else '.jpg'
            file_hash = hashlib.md5(f"{upload_file.filename}_{upload_file.size}".encode()).hexdigest()[:8]
            filename = f"{file_hash}{file_ext}"
            file_path = os.path.join(save_dir, filename)
            
            # 异步保存文件
            async with aiofiles.open(file_path, 'wb') as f:
                content = await upload_file.read()
                await f.write(content)
            
            logger.info(f"文件保存成功: {file_path}")
            return True, file_path, ""
            
        except Exception as e:
            error_msg = f"保存文件失败: {str(e)}"
            logger.error(error_msg)
            return False, "", error_msg
    
    @staticmethod
    def file_to_base64(file_path: str) -> Optional[str]:
        """
        将文件转换为base64编码
        
        Args:
            file_path: 文件路径
            
        Returns:
            base64编码的字符串，失败返回None
        """
        try:
            with open(file_path, 'rb') as f:
                content = f.read()
                return base64.b64encode(content).decode('utf-8')
        except Exception as e:
            logger.error(f"文件转base64失败: {str(e)}")
            return None
    
    @staticmethod
    def bytes_to_base64(data: bytes) -> str:
        """
        将字节数据转换为base64编码
        
        Args:
            data: 字节数据
            
        Returns:
            base64编码的字符串
        """
        return base64.b64encode(data).decode('utf-8')
    
    @staticmethod
    def base64_to_bytes(base64_str: str) -> Optional[bytes]:
        """
        将base64字符串转换为字节数据
        
        Args:
            base64_str: base64编码的字符串
            
        Returns:
            字节数据，失败返回None
        """
        try:
            return base64.b64decode(base64_str)
        except Exception as e:
            logger.error(f"base64解码失败: {str(e)}")
            return None
    
    @staticmethod
    def get_file_mime_type(file_path: str) -> str:
        """
        获取文件的MIME类型
        
        Args:
            file_path: 文件路径
            
        Returns:
            MIME类型字符串
        """
        mime_type, _ = mimetypes.guess_type(file_path)
        return mime_type or 'application/octet-stream'
    
    @staticmethod
    def ensure_dir(dir_path: str) -> bool:
        """
        确保目录存在
        
        Args:
            dir_path: 目录路径
            
        Returns:
            是否成功
        """
        try:
            os.makedirs(dir_path, exist_ok=True)
            return True
        except Exception as e:
            logger.error(f"创建目录失败: {str(e)}")
            return False
    
    @staticmethod
    def clean_filename(filename: str) -> str:
        """
        清理文件名，移除非法字符
        
        Args:
            filename: 原始文件名
            
        Returns:
            清理后的文件名
        """
        import re
        # 移除非法字符
        cleaned = re.sub(r'[<>:"/\\|?*]', '_', filename)
        # 限制长度
        if len(cleaned) > 100:
            name, ext = os.path.splitext(cleaned)
            cleaned = name[:100-len(ext)] + ext
        return cleaned
    
    @staticmethod
    def get_file_size(file_path: str) -> int:
        """
        获取文件大小
        
        Args:
            file_path: 文件路径
            
        Returns:
            文件大小（字节），失败返回-1
        """
        try:
            return os.path.getsize(file_path)
        except Exception:
            return -1
